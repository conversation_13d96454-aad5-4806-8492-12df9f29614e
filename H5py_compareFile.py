import h5py
import numpy as np
import json
import os
import argparse

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Compare two NDE files and generate a differences report')
    parser.add_argument('--file1', type=str,
                       default="./ndes/nde/From Benoit Cabirol/Cor_Tube_5mm_Hydro_rast_all_0001.nde",
                       help='Path to the first NDE file')
    parser.add_argument('--file2', type=str,
                       default="./ndes/nde/From Benoit Cabirol/Pipe_10mm_Flexoform_Rast_All.nde",
                       help='Path to the second NDE file')
    parser.add_argument('--output', type=str, default="./",
                       help='Output directory for results')
    return parser.parse_args()

# Parse arguments
args = parse_arguments()
file1_path = args.file1
file2_path = args.file2
output_dir = args.output

# Ensure output directory exists
os.makedirs(output_dir, exist_ok=True)

# Output files in the specified directory
output_path = os.path.join(output_dir, "differences.txt")

differences = []
file1_structure = []
file2_structure = []

def explore_h5(file_path, output_list):
    """Explore the complete structure of an H5 file and store it in output_list"""
    with h5py.File(file_path, 'r') as f:
        def visitor(name, obj):
            if isinstance(obj, h5py.Dataset):
                info = {
                    'path': name,
                    'type': 'Dataset',
                    'shape': str(obj.shape),
                    'dtype': str(obj.dtype)
                }
                # Add attributes if any
                if len(obj.attrs) > 0:
                    info['attrs'] = {k: str(v) for k, v in obj.attrs.items()}
                
                # For small datasets, add sample data
                if len(obj.shape) == 0 or (len(obj.shape) == 1 and obj.shape[0] < 10):
                    try:
                        # Try to read the data with error handling
                        if obj.dtype.kind in ['S', 'U']:  # String data
                            try:
                                data = obj[()]
                                if isinstance(data, bytes):
                                    data = data.decode('utf-8', errors='replace')
                                elif isinstance(data, np.ndarray) and data.dtype.kind == 'S':
                                    data = data.astype(str)

                                data_str = str(data)
                                # If it looks like JSON, try to parse it
                                if data_str.startswith('{') or data_str.startswith('['):
                                    try:
                                        json.loads(data_str)
                                        info['data'] = "JSON data (too large to display)"
                                    except:
                                        info['data'] = data_str[:100] + "..." if len(data_str) > 100 else data_str
                                else:
                                    info['data'] = data_str[:100] + "..." if len(data_str) > 100 else data_str
                            except Exception as e:
                                info['data'] = f"String read error: {str(e)}"
                        else:
                            # Numeric or other data
                            try:
                                data = obj[()]
                                info['data'] = str(data)
                            except Exception as e:
                                info['data'] = f"Data read error: {str(e)}"
                    except Exception as e:
                        info['data'] = f"Unable to read data: {str(e)}"
                
                output_list.append(info)
            elif isinstance(obj, h5py.Group):
                info = {
                    'path': name,
                    'type': 'Group',
                    'keys': list(obj.keys())
                }
                # Add attributes if any
                if len(obj.attrs) > 0:
                    info['attrs'] = {k: str(v) for k, v in obj.attrs.items()}
                
                output_list.append(info)
        
        # Visit all items in the file
        f.visititems(visitor)
        
        # Add root level items
        info = {
            'path': '/',
            'type': 'Group',
            'keys': list(f.keys())
        }
        if len(f.attrs) > 0:
            info['attrs'] = {k: str(v) for k, v in f.attrs.items()}
        
        output_list.append(info)

def compare_datasets(dset1, dset2, path):
    """Compare two datasets with robust error handling"""
    diff = []

    # Compare shapes
    if dset1.shape != dset2.shape:
        diff.append(f"{path}: shape mismatch {dset1.shape} vs {dset2.shape}")
        return diff

    # Compare dtypes
    if dset1.dtype != dset2.dtype:
        diff.append(f"{path}: dtype mismatch {dset1.dtype} vs {dset2.dtype}")

    # Try to compare data with error handling
    try:
        # For small datasets, try direct comparison
        if dset1.size < 1000000:  # Limit to 1M elements
            data1 = dset1[()]
            data2 = dset2[()]

            # Handle different data types
            if dset1.dtype.kind in ['S', 'U']:  # String data
                if not np.array_equal(data1, data2):
                    diff.append(f"{path}: string data mismatch")
            elif dset1.dtype.kind in ['f', 'c']:  # Float or complex data
                if not np.allclose(data1, data2, rtol=1e-10, atol=1e-10, equal_nan=True):
                    diff.append(f"{path}: numerical data mismatch (within tolerance)")
            else:  # Integer or other data
                if not np.array_equal(data1, data2):
                    diff.append(f"{path}: data mismatch")
        else:
            # For large datasets, compare metadata only
            diff.append(f"{path}: large dataset - only metadata compared (size: {dset1.size})")

    except (OSError, ValueError, MemoryError) as e:
        # Handle conversion errors or memory issues
        diff.append(f"{path}: cannot compare data - {str(e)}")

        # Try to compare basic statistics if possible
        try:
            if dset1.dtype.kind in ['f', 'i']:  # Numeric data
                # Compare a small sample
                sample_size = min(100, dset1.size)
                if sample_size > 0:
                    sample1 = dset1.flat[:sample_size]
                    sample2 = dset2.flat[:sample_size]
                    if not np.array_equal(sample1, sample2):
                        diff.append(f"{path}: sample data differs")
        except Exception:
            diff.append(f"{path}: unable to compare even sample data")

    return diff

def compare_h5_files():
    with h5py.File(file1_path, 'r') as f1, h5py.File(file2_path, 'r') as f2:
        def walk_groups(g1, g2, prefix=""):
            diff = []
            keys1 = set(g1.keys())
            keys2 = set(g2.keys())
            all_keys = sorted(keys1.union(keys2))
            for key in all_keys:
                path = f"{prefix}/{key}".strip('/')
                if key not in g1:
                    diff.append(f"{path}: missing in file1")
                    continue
                if key not in g2:
                    diff.append(f"{path}: missing in file2")
                    continue
                obj1, obj2 = g1[key], g2[key]
                if isinstance(obj1, h5py.Dataset) and isinstance(obj2, h5py.Dataset):
                    diff += compare_datasets(obj1, obj2, path)
                elif isinstance(obj1, h5py.Group) and isinstance(obj2, h5py.Group):
                    diff += walk_groups(obj1, obj2, path)
                else:
                    diff.append(f"{path}: type mismatch")
            return diff

        return walk_groups(f1, f2)

# Explore both files
try:
    print(f"Exploring {file1_path}...")
    explore_h5(file1_path, file1_structure)
    print(f"[OK] File 1 explored successfully ({len(file1_structure)} items found)")
except Exception as e:
    print(f"[ERROR] Error exploring file 1: {e}")
    file1_structure = []

try:
    print(f"Exploring {file2_path}...")
    explore_h5(file2_path, file2_structure)
    print(f"[OK] File 2 explored successfully ({len(file2_structure)} items found)")
except Exception as e:
    print(f"[ERROR] Error exploring file 2: {e}")
    file2_structure = []

# Compare files
print("Comparing files...")
try:
    differences = compare_h5_files()
    print(f"[OK] Comparison completed ({len(differences)} differences found)")
except Exception as e:
    print(f"[ERROR] Error during comparison: {e}")
    differences = [f"Comparison failed: {str(e)}"]

# Write differences to file
with open(output_path, 'w') as f:
    f.write(f"Comparison between:\n{file1_path}\nand\n{file2_path}\n\n")
    f.write("=== DIFFERENCES ===\n")
    for line in differences:
        f.write(line + '\n')

# Write detailed structure to separate files
file1_name = os.path.basename(file1_path).replace('.', '_')
file2_name = os.path.basename(file2_path).replace('.', '_')

structure1_path = os.path.join(output_dir, f"structure_{file1_name}.json")
structure2_path = os.path.join(output_dir, f"structure_{file2_name}.json")

with open(structure1_path, 'w') as f:
    json.dump(file1_structure, f, indent=2)

with open(structure2_path, 'w') as f:
    json.dump(file2_structure, f, indent=2)

print(f"Differences written to {output_path}")
print(f"Structure of {file1_path} written to {structure1_path}")
print(f"Structure of {file2_path} written to {structure2_path}")

# Print summary
print(f"\nFound {len(differences)} differences")
print(f"File 1 has {len(file1_structure)} items")
print(f"File 2 has {len(file2_structure)} items")
