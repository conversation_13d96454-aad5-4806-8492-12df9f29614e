# 🔬 Outils de Traitement et Visualisation d'Images Médicales

Ce projet regroupe des scripts Python pour le traitement, la conversion, la copie et la visualisation d'images médicales (NIfTI, PNG, NDE), avec une interface graphique centralisée.

## 📋 Table des Matières

- [Installation](#installation)
- [Interface Graphique](#interface-graphique)
- [Scripts de Traitement](#scripts-de-traitement)
- [Scripts de Visualisation](#scripts-de-visualisation)
- [Scripts Utilitaires](#scripts-utilitaires)
- [Scripts de Copie](#scripts-de-copie)
- [Scripts avancés NDE (HDF5)](#scripts-avances-nde-hdf5)
- [Utilisation](#utilisation)
- [Dépannage](#dépannage)
- [Auteur](#auteur)

## 🛠️ Installation

- Python 3.7+
- Installez les dépendances :
  ```bash
  pip install nibabel opencv-python numpy matplotlib tkinter pillow
  ```

## 🖥️ Interface Graphique

### `interface_graphique.py`
Interface centrale pour lancer tous les scripts du projet via une interface conviviale :
- Sélection du script à exécuter
- Paramétrage par boîtes de dialogue
- Affichage de la sortie en temps réel

**Lancement :**
```bash
python interface_graphique.py
```

## 📦 Scripts de Traitement

- **`conversion_dataset_2d_to_3d.py`**  
  Convertit un dataset d'images 2D (PNG) et leurs labels en volumes 3D NIfTI pour nnU-Net, en groupant par forme d'image.

- **`conversion_inference_2d_to_3d.py`**  
  Regroupe des images PNG 2D en volumes 3D NIfTI pour l'inférence nnU-Net (sans labels).

## 👁️ Scripts de Visualisation

- **`nifti_view_volume.py`**  
  Visualiseur interactif de volumes NIfTI : navigation par slice, sauvegarde, détection automatique de masque ou d'intensité.

- **`view_png_folder.py`**  
  Visualiseur interactif pour dossiers d'images PNG, avec détection automatique de masques de segmentation.

- **`view_mask.py`**  
  Affichage côte à côte d'un masque PNG original et d'une version normalisée pour une meilleure visibilité.

- **`view_labels.py`**  
  Analyse et affiche les valeurs uniques présentes dans chaque image de label d'un dossier.

- **`nde_viewer.py`**  
  Visualisation interactive de fichiers NDE (HDF5) : navigation par slice et dataset, support des données d'amplitude, affichage d'infos détaillées.

## 🔧 Scripts Utilitaires

- **`nifti_check_shape.py`**  
  Affiche la forme (dimensions) et le nombre de dimensions d'un fichier NIfTI.

- **`nifti_transpose.py`**  
  Change l'orientation des axes d'un volume NIfTI (ex : (Z, Y, X) → (Y, X, Z)).

- **`util_directory_tree.py`**  
  Affiche l'arborescence d'un dossier (avec ou sans fichiers) et sauvegarde le résultat dans `structure.txt`.

## 📋 Scripts de Copie

- **`copy_random_images.py`**  
  Copie aléatoirement un nombre donné d'images d'un dossier source vers un dossier destination, avec ajout de préfixe/suffixe.

- **`copy_matching_files.py`**  
  Copie les fichiers ayant le même nom entre deux dossiers (A et B) vers deux dossiers de destination (C et D), en ajoutant un préfixe.

- **`copy_by_resolution.py`**  
  Copie les fichiers du dossier source par groupes de résolution d'image, en sélectionnant jusqu'à 10 fichiers par résolution, et copie les correspondants du dossier de référence.

## 🧪 Scripts avancés NDE (HDF5)

- **`H5py_NdeToImages.py`**  
  Extrait et convertit les données d'amplitude de fichiers NDE (HDF5) en images PNG (grayscale et colorisées), avec gestion automatique de la structure du fichier. Permet la génération de masques et d'annotations si disponibles.

- **`H5py_process_nde.py`**  
  Traite automatiquement tous les fichiers NDE d'un dossier (et sous-dossiers), convertit en images, évite les doublons, et gère la création des dossiers de sortie.

- **`H5py_compareFile.py`**  
  Compare deux fichiers NDE (HDF5) et génère un rapport détaillé des différences (structure, datasets, métadonnées, valeurs). Produit aussi des fichiers JSON de structure.

- **`H5py_openFile.py`**  
  Ouvre et explore la structure d'un fichier NDE (groupes, datasets, métadonnées), affiche les informations utiles pour le debug ou l'analyse.

- **`H5py_enlever_dossier_inutile.py`**  
  Nettoie la structure des dossiers d'images générées : déplace le contenu utile, supprime les dossiers "complete" et réorganise les arborescences.

- **`H5py_inversion.py`**  
  Inverse les couleurs de toutes les images PNG uint8 d'un dossier (255 - valeur_pixel), avec création automatique de backup.

## 🚀 Utilisation

- **Via l'interface graphique (recommandé)**
  1. Lancez `interface_graphique.py`
  2. Sélectionnez le script
  3. Configurez les paramètres
  4. Cliquez sur "Exécuter"

- **Via la ligne de commande**
  ```bash
  python nom_du_script.py
  ```

## 🔍 Dépannage

- **Erreur d'encodage** : Vérifiez les chemins et l'encodage UTF-8.
- **Fichier non trouvé** : Utilisez les sélecteurs de fichiers de l'interface.
- **Mémoire insuffisante** : Limitez le nombre d'images chargées.
- **Visualiseur ne s'ouvre pas** : Vérifiez l'installation de matplotlib, tkinter, pillow.

## 👨‍💻 Auteur

Gabriel Forest – 2025-06-18

---

*Outils pour la détection de corrosion et le traitement d'images médicales.*
