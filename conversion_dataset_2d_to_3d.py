"""
Script simple pour convertir des datasets 2D (images PNG organisées en dossiers) 
vers des volumes 3D NIfTI compatibles avec nnU-Net.

Auteur: Gabriel Forest
Date: 2025-06-16
"""

import os
import numpy as np
import nibabel as nib
from PIL import Image
from pathlib import Path
from natsort import natsorted
import json


def convert_png_to_nifti_3d(images_root, labels_root, output_dataset_dir,
                           dataset_name="Dataset013_test4labeldifferentUint8_3d",
                           file_ending=".nii.gz"):
    """
    Convertit des images PNG 2D individuelles en volumes 3D NIfTI.
    Groupe les images par forme (shape) pour créer des volumes 3D homogènes.
    Chaque forme unique devient un volume 3D séparé.

    Format de nommage conforme nnU-Net:
    - Images: imagesTr/001_0000.nii.gz, 002_0000.nii.gz, ...
    - Labels: labelsTr/001.nii.gz, 002.nii.gz, ...

    Args:
        images_root: Chemin vers le dossier contenant les images PNG
        labels_root: Chemin vers le dossier contenant les labels PNG
        output_dataset_dir: Dossier de sortie pour le dataset nnU-Net
        dataset_name: Nom du dataset
        file_ending: Extension des fichiers NIfTI
    """
    # Création de la structure de sortie
    output_dir = Path(output_dataset_dir) / dataset_name
    imagesTr_dir = output_dir / "imagesTr"
    labelsTr_dir = output_dir / "labelsTr"
    imagesTr_dir.mkdir(parents=True, exist_ok=True)
    labelsTr_dir.mkdir(parents=True, exist_ok=True)

    # Récupération des fichiers PNG
    img_files = natsorted([f for f in os.listdir(images_root) if f.endswith('.png')])
    lbl_files = natsorted([f for f in os.listdir(labels_root) if f.endswith('.png')])

    print(f"📂 Trouvé {len(img_files)} images et {len(lbl_files)} labels")

    if len(img_files) != len(lbl_files):
        print(f"⚠️ ATTENTION: Nombre d'images ({len(img_files)}) != nombre de labels ({len(lbl_files)})")
        # Prendre le minimum pour éviter les erreurs
        min_files = min(len(img_files), len(lbl_files))
        img_files = img_files[:min_files]
        lbl_files = lbl_files[:min_files]
        print(f"🔧 Utilisation de {min_files} fichiers")

    # Analyser toutes les images pour grouper par forme
    print("🔍 Analyse des formes d'images pour groupement...")
    shape_groups = {}  # {shape: [list of file indices]}

    for i, img_file in enumerate(img_files):
        img_path = Path(images_root) / img_file
        img = np.array(Image.open(img_path).convert("L"))
        shape = img.shape

        if shape not in shape_groups:
            shape_groups[shape] = []
        shape_groups[shape].append(i)

    print(f"📊 Détection de {len(shape_groups)} formes d'images différentes:")
    for shape, indices in shape_groups.items():
        print(f"   📏 {shape}: {len(indices)} images")

    print(f"🔄 Création de {len(shape_groups)} volumes 3D (un par forme)...")

    vol_idx = 0
    for shape, indices in shape_groups.items():
        vol_idx += 1

        volume_img = []
        volume_lbl = []

        print(f"📏 Traitement du volume {vol_idx} - Forme: {shape} - {len(indices)} slices")

        # Charger toutes les images de cette forme
        for i in indices:
            img_path = Path(images_root) / img_files[i]
            lbl_path = Path(labels_root) / lbl_files[i]

            img = np.array(Image.open(img_path).convert("L"))  # grayscale
            lbl = np.array(Image.open(lbl_path))

            # Vérification de sécurité (toutes les images de ce groupe devraient avoir la même forme)
            if img.shape != shape:
                print(f"⚠️ Forme inattendue pour {img_files[i]}: {img.shape} vs {shape}")
                continue

            volume_img.append(img)
            volume_lbl.append(lbl)

        # Convertir en arrays numpy
        try:
            vol_img_np = np.stack(volume_img, axis=0)
            vol_lbl_np = np.stack(volume_lbl, axis=0)
        except ValueError as e:
            print(f"❌ Erreur lors de la création du volume {vol_idx}: {e}")
            print("🔍 Vérification des formes des images:")
            for j, (img, lbl) in enumerate(zip(volume_img, volume_lbl)):
                print(f"  Slice {j}: img={img.shape}, lbl={lbl.shape}")
            continue

        # Nom du volume conforme nnU-Net: case_XXX
        case_id = f"{vol_idx:03d}"

        # Sauvegarder les volumes NIfTI avec nommage conforme nnU-Net
        # Image: case_XXX_0000.nii.gz dans imagesTr/
        # Label: case_XXX.nii.gz dans labelsTr/
        nib.save(nib.Nifti1Image(vol_img_np, affine=np.eye(4)),
                imagesTr_dir / f"{case_id}_0000{file_ending}")
        nib.save(nib.Nifti1Image(vol_lbl_np.astype(np.uint8), affine=np.eye(4)),
                labelsTr_dir / f"{case_id}{file_ending}")

        print(f"✅ Case {case_id} créé: {len(indices)} slices de forme {shape}")
        print(f"   📄 Image: {case_id}_0000{file_ending}")
        print(f"   🏷️  Label: {case_id}{file_ending}")

    num_volumes = len(shape_groups)
    print(f"📦 Total volumes créés: {num_volumes}")

    # === Génération du dataset.json ===
    dataset_json = {
        "channel_names": {
            "0": "ultrasound"
        },
        "labels": {
            "background": 0,
            "frontwall": 1,
            "backwall": 2,
            "flaw": 3,
            "indication": 4
        },
        "numTraining": num_volumes,
        "file_ending": file_ending,
        "overwrite_image_reader_writer": "SimpleITKIO"
    }

    with open(output_dir / "dataset.json", "w") as f:
        json.dump(dataset_json, f, indent=4)

    print(f"\n📄 dataset.json créé dans : {output_dir}")
    print(f"📦 Total volumes convertis : {num_volumes}")
    return True


# ========================================
# CONFIGURATION - MODIFIEZ VOS CHEMINS ICI
# ========================================

# Vos chemins de données
IMAGES_ROOT = r"C:\Users\<USER>\OneDrive - EvidentScientific\Documents\4Corrosion\Dataset\nnUnet\nnUNet_raw\Dataset014_test4labeldifferentUint8\imagesTr"
LABELS_ROOT = r"C:\Users\<USER>\OneDrive - EvidentScientific\Documents\4Corrosion\Dataset\nnUnet\nnUNet_raw\Dataset014_test4labeldifferentUint8\labelsTr"
OUTPUT_DIR = r"C:\Users\<USER>\OneDrive - EvidentScientific\Documents\4Corrosion\Dataset\nnUnet\nnUNet_raw"

# Configuration du dataset
DATASET_NAME = "Dataset015_test4labeldifferentUint8_3d"
FILE_ENDING = ".nii.gz"


if __name__ == "__main__":
    print("🎯 CONVERSION DATASET 2D VERS 3D POUR nnU-Net")
    print("=" * 50)
    print(f"📂 Images: {IMAGES_ROOT}")
    print(f"🏷️  Labels: {LABELS_ROOT}")
    print(f"📤 Output: {OUTPUT_DIR}")
    print(f"📋 Dataset: {DATASET_NAME}")
    print("🔄 Mode: Un volume par forme d'image")
    print("=" * 50)

    # Vérification des chemins
    if not Path(IMAGES_ROOT).exists():
        print(f"❌ ERREUR: Dossier images introuvable: {IMAGES_ROOT}")
        exit(1)

    if not Path(LABELS_ROOT).exists():
        print(f"❌ ERREUR: Dossier labels introuvable: {LABELS_ROOT}")
        exit(1)

    # Lancement de la conversion
    success = convert_png_to_nifti_3d(
        images_root=IMAGES_ROOT,
        labels_root=LABELS_ROOT,
        output_dataset_dir=OUTPUT_DIR,
        dataset_name=DATASET_NAME,
        file_ending=FILE_ENDING
    )

    if success:
        print("\n🎉 Conversion terminée avec succès!")
    else:
        print("\n💥 Échec de la conversion!")
