"""
Visualiseur interactif pour fichiers .nde (HDF5) - Version stable
Auteur: Gabriel Forest
Date: 2025-06-18

Version optimisée avec corrections:
- Rotation de 90° corrigée
- Démarrage à la première slice
- Compatibilité avec tous les fichiers NDE
- Support des données d'amplitude int16/uint16
"""

import h5py
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.widgets import Button
from pathlib import Path
import argparse
import sys

class NDEViewerStable:
    def __init__(self, file_path):
        """
        Initialise le visualiseur pour un fichier .nde
        
        Args:
            file_path (str): Chemin vers le fichier .nde
        """
        self.file_path = Path(file_path)
        self.current_slice = 0
        self.current_dataset = 0
        self.datasets = []
        self.data_cache = {}
        self.auto_contrast = True  # Mode contraste automatique
        
        # Vérifier que le fichier existe
        if not self.file_path.exists():
            raise FileNotFoundError(f"Fichier non trouvé: {self.file_path}")
        
        # Charger les informations sur les datasets
        self._load_datasets_info()
        
        if not self.datasets:
            raise ValueError("Aucun dataset d'image trouvé dans le fichier .nde")
        
        # Initialiser l'interface
        self._setup_interface()
        self._load_current_data()
        self._update_display()
        
    def _load_datasets_info(self):
        """Charge les informations sur tous les datasets d'images disponibles"""
        with h5py.File(self.file_path, 'r') as f:
            def find_datasets(name, obj):
                if isinstance(obj, h5py.Dataset):
                    # Critères pour identifier une image:
                    # - Au moins 2 dimensions
                    # - Taille raisonnable
                    # - Exclure les types de données non supportés
                    if (len(obj.shape) >= 2 and obj.size > 1000 and
                        not str(obj.dtype).startswith('|V')):  # Exclure les types variables non supportés

                        # Vérifier si on peut lire les données
                        try:
                            # Test de lecture d'un petit échantillon
                            if len(obj.shape) == 3:
                                _ = obj[0, 0:2, 0:2]
                            else:
                                _ = obj[0:2, 0:2]

                            self.datasets.append({
                                'name': name,
                                'path': name,
                                'shape': obj.shape,
                                'dtype': obj.dtype,
                                'is_3d': len(obj.shape) == 3,
                                'num_slices': obj.shape[0] if len(obj.shape) == 3 else 1
                            })
                        except Exception as e:
                            print(f"⚠️ Dataset ignoré '{name}': {e}")

            f.visititems(find_datasets)

        # Trier par nom pour un ordre cohérent
        self.datasets.sort(key=lambda x: x['name'])

        print(f"Datasets trouvés:")
        for i, ds in enumerate(self.datasets):
            print(f"  {i}: {ds['name']} - Shape: {ds['shape']} - Type: {ds['dtype']}")
    
    def _setup_interface(self):
        """Configure l'interface matplotlib"""
        # Créer la figure avec une taille fixe
        self.fig = plt.figure(figsize=(16, 10))
        self.fig.suptitle(f"Visualiseur NDE - {self.file_path.name}", fontsize=14)
        
        # Créer les axes avec des positions fixes
        self.ax = self.fig.add_axes([0.1, 0.15, 0.75, 0.75])  # [left, bottom, width, height]
        self.cbar_ax = self.fig.add_axes([0.87, 0.15, 0.03, 0.75])  # Colorbar fixe
        
        # Connecter les événements clavier
        self.fig.canvas.mpl_connect('key_press_event', self._on_key_press)
        
        # Ajouter des boutons de navigation
        self._add_navigation_buttons()
        
        # Afficher les contrôles
        self._show_controls()
    
    def _add_navigation_buttons(self):
        """Ajoute des boutons de navigation"""
        # Boutons en bas
        btn_height = 0.04
        btn_width = 0.08
        y_pos = 0.05
        
        # Bouton slice précédente
        ax_prev = self.fig.add_axes([0.2, y_pos, btn_width, btn_height])
        self.btn_prev = Button(ax_prev, '← Slice')
        self.btn_prev.on_clicked(self._prev_slice)
        
        # Bouton slice suivante
        ax_next = self.fig.add_axes([0.3, y_pos, btn_width, btn_height])
        self.btn_next = Button(ax_next, 'Slice →')
        self.btn_next.on_clicked(self._next_slice)
        
        # Bouton dataset précédent
        ax_prev_ds = self.fig.add_axes([0.45, y_pos, btn_width, btn_height])
        self.btn_prev_ds = Button(ax_prev_ds, '↑ Dataset')
        self.btn_prev_ds.on_clicked(self._prev_dataset)
        
        # Bouton dataset suivant
        ax_next_ds = self.fig.add_axes([0.55, y_pos, btn_width, btn_height])
        self.btn_next_ds = Button(ax_next_ds, 'Dataset ↓')
        self.btn_next_ds.on_clicked(self._next_dataset)
        
        # Bouton info
        ax_info = self.fig.add_axes([0.7, y_pos, btn_width, btn_height])
        self.btn_info = Button(ax_info, 'Info')
        self.btn_info.on_clicked(self._show_info_callback)

        # Bouton contraste
        ax_contrast = self.fig.add_axes([0.8, y_pos, btn_width, btn_height])
        self.btn_contrast = Button(ax_contrast, 'Contraste')
        self.btn_contrast.on_clicked(self._toggle_contrast)
    
    def _prev_slice(self, _event):
        """Slice précédente"""
        dataset_info = self.datasets[self.current_dataset]
        if not dataset_info['is_3d']:
            print(f"ℹ️ Dataset '{dataset_info['name']}' est 2D - pas de slices à naviguer")
            return
        if self.current_slice > 0:
            self.current_slice -= 1
            self._update_display()
        else:
            print(f"ℹ️ Déjà à la première slice (1/{dataset_info['num_slices']})")

    def _next_slice(self, _event):
        """Slice suivante"""
        dataset_info = self.datasets[self.current_dataset]
        if not dataset_info['is_3d']:
            print(f"ℹ️ Dataset '{dataset_info['name']}' est 2D - pas de slices à naviguer")
            return
        if self.current_slice < dataset_info['num_slices'] - 1:
            self.current_slice += 1
            self._update_display()
        else:
            print(f"ℹ️ Déjà à la dernière slice ({dataset_info['num_slices']}/{dataset_info['num_slices']})")
    
    def _prev_dataset(self, _event):
        """Dataset précédent"""
        if self.current_dataset > 0:
            self.current_dataset -= 1
            self._load_current_data()
            self._update_display()
    
    def _next_dataset(self, _event):
        """Dataset suivant"""
        if self.current_dataset < len(self.datasets) - 1:
            self.current_dataset += 1
            self._load_current_data()
            self._update_display()
    
    def _show_info_callback(self, _event):
        """Callback pour afficher les infos"""
        self._show_info()

    def _toggle_contrast(self, _event):
        """Bascule entre contraste automatique et manuel"""
        self.auto_contrast = not self.auto_contrast
        mode = "automatique" if self.auto_contrast else "manuel"
        print(f"🎨 Mode contraste: {mode}")
        self._update_display()
    
    def _load_current_data(self):
        """Charge les données du dataset actuel"""
        dataset_info = self.datasets[self.current_dataset]
        dataset_path = dataset_info['path']

        # Utiliser le cache si disponible
        if dataset_path not in self.data_cache:
            print(f"Chargement du dataset: {dataset_path}")
            with h5py.File(self.file_path, 'r') as f:
                self.data_cache[dataset_path] = f[dataset_path][...]

        self.current_data = self.data_cache[dataset_path]

        # Ajuster l'index de slice si nécessaire
        if dataset_info['is_3d']:
            max_slice = dataset_info['num_slices'] - 1
            self.current_slice = min(self.current_slice, max_slice)

            # Si c'est la première fois qu'on charge ce dataset, commencer à la première slice
            if not hasattr(self, '_initialized_datasets'):
                self._initialized_datasets = set()

            if dataset_path not in self._initialized_datasets:
                self.current_slice = 0  # Toujours commencer à la première slice
                self._initialized_datasets.add(dataset_path)
                print(f"📍 Démarrage à la slice 1/{dataset_info['num_slices']}")
        else:
            self.current_slice = 0



    def _get_current_image(self):
        """Récupère l'image actuelle à afficher avec correction de rotation"""
        dataset_info = self.datasets[self.current_dataset]

        if dataset_info['is_3d']:
            # Pour les données 3D, extraire la slice actuelle
            # Shape est (num_slices, height, width) donc slice = index 0
            if self.current_slice < self.current_data.shape[0]:
                img = self.current_data[self.current_slice, :, :]
            else:
                img = self.current_data[0, :, :]
        else:
            # Pour les données 2D
            img = self.current_data

        # Correction de la rotation de 90 degrés
        # Rotation de -90 degrés (sens horaire) pour corriger l'orientation
        img_corrected = np.rot90(img, k=-1)  # k=-1 pour rotation de -90°

        return img_corrected
    
    def _update_display(self):
        """Met à jour l'affichage de manière stable"""
        # Effacer seulement le contenu de l'axe principal
        self.ax.clear()
        self.cbar_ax.clear()
        
        # Récupérer l'image actuelle
        img = self._get_current_image()
        dataset_info = self.datasets[self.current_dataset]
        
        # Déterminer la colormap appropriée et la normalisation
        # Toutes les données NDE sont des amplitudes (int16/uint16)
        cmap = 'viridis'
        img_min, img_max = np.min(img), np.max(img)

        if img_max == img_min:
            # Image uniforme
            vmin, vmax = img_min, img_min + 1
        elif self.auto_contrast and img_max > 0 and img_min >= 0:
            # Mode contraste automatique pour les images sombres
            # Utiliser les percentiles pour éviter les valeurs aberrantes
            p1, p99 = np.percentile(img[img > 0], [1, 99]) if np.any(img > 0) else (img_min, img_max)
            vmin, vmax = p1, p99
            if vmax == vmin:
                vmin, vmax = img_min, img_max
        else:
            # Mode contraste manuel - utiliser toute la plage
            vmin, vmax = img_min, img_max
        
        # Afficher l'image avec aspect fixe
        im = self.ax.imshow(img, cmap=cmap, vmin=vmin, vmax=vmax, 
                           aspect='equal', interpolation='nearest')
        
        # Créer la colorbar dans l'axe dédié
        self.fig.colorbar(im, cax=self.cbar_ax)
        
        # Titre avec informations
        title = f"{dataset_info['name']}"
        if dataset_info['is_3d']:
            title += f" - Slice {self.current_slice+1}/{dataset_info['num_slices']} (3D)"
        else:
            title += f" (2D - pas de slices)"
        title += f"\nShape: {img.shape} | Type: {dataset_info['dtype']} | Min: {np.min(img)} | Max: {np.max(img)}"
        
        self.ax.set_title(title, fontsize=10)
        self.ax.set_xlabel('X')
        self.ax.set_ylabel('Y')
        
        # Redraw
        self.fig.canvas.draw()
    
    def _on_key_press(self, event):
        """Gestion des événements clavier"""
        dataset_info = self.datasets[self.current_dataset]
        
        if event.key == 'right' or event.key == 'd':
            # Slice suivante
            if not dataset_info['is_3d']:
                print(f"ℹ️ Dataset '{dataset_info['name']}' est 2D - pas de slices à naviguer")
            elif self.current_slice < dataset_info['num_slices'] - 1:
                self.current_slice += 1
                self._update_display()
                print(f"➡️ Slice {self.current_slice+1}/{dataset_info['num_slices']}")
            else:
                print(f"ℹ️ Déjà à la dernière slice ({dataset_info['num_slices']}/{dataset_info['num_slices']})")

        elif event.key == 'left' or event.key == 'a':
            # Slice précédente
            if not dataset_info['is_3d']:
                print(f"ℹ️ Dataset '{dataset_info['name']}' est 2D - pas de slices à naviguer")
            elif self.current_slice > 0:
                self.current_slice -= 1
                self._update_display()
                print(f"⬅️ Slice {self.current_slice+1}/{dataset_info['num_slices']}")
            else:
                print(f"ℹ️ Déjà à la première slice (1/{dataset_info['num_slices']})")
        
        elif event.key == 'up' or event.key == 'w':
            # Dataset suivant
            if self.current_dataset < len(self.datasets) - 1:
                self.current_dataset += 1
                self._load_current_data()
                self._update_display()

        elif event.key == 'down' or event.key == 's':
            # Dataset précédent
            if self.current_dataset > 0:
                self.current_dataset -= 1
                self._load_current_data()
                self._update_display()
        
        elif event.key == 'home':
            # Première slice
            if dataset_info['is_3d']:
                self.current_slice = 0
                self._update_display()
        
        elif event.key == 'end':
            # Dernière slice
            if dataset_info['is_3d']:
                self.current_slice = dataset_info['num_slices'] - 1
                self._update_display()
        
        elif event.key == 'i':
            # Informations détaillées
            self._show_info()
        
        elif event.key == 'h':
            # Aide
            self._show_controls()
        
        elif event.key == 'q' or event.key == 'escape':
            # Quitter
            plt.close()
    
    def _show_controls(self):
        """Affiche les contrôles disponibles"""
        dataset_info = self.datasets[self.current_dataset]
        controls = f"""
        CONTRÔLES DISPONIBLES:
        ← → (ou A/D) : Slice précédente/suivante {'(datasets 3D uniquement)' if not dataset_info['is_3d'] else ''}
        ↑ ↓ (ou W/S) : Dataset précédent/suivant
        Home/End     : Première/dernière slice {'(datasets 3D uniquement)' if not dataset_info['is_3d'] else ''}
        I            : Informations détaillées
        H            : Afficher cette aide
        Q/Escape     : Quitter

        Utilisez aussi les boutons en bas de l'écran.

        📊 Dataset actuel: {dataset_info['name']} ({'3D' if dataset_info['is_3d'] else '2D'})
        """
        print(controls)
    
    def _show_info(self):
        """Affiche des informations détaillées sur l'image actuelle"""
        img = self._get_current_image()
        dataset_info = self.datasets[self.current_dataset]
        
        info = f"""
        INFORMATIONS DÉTAILLÉES:
        Fichier: {self.file_path}
        Dataset: {dataset_info['name']}
        Shape complète: {dataset_info['shape']}
        Type de données: {dataset_info['dtype']}
        Image actuelle: {img.shape}
        Valeurs: Min={np.min(img)}, Max={np.max(img)}, Moyenne={np.mean(img):.2f}
        """
        if dataset_info['is_3d']:
            info += f"Slice: {self.current_slice+1}/{dataset_info['num_slices']}"
        
        print(info)
    
    def show(self):
        """Affiche le visualiseur"""
        print(f"Ouverture du visualiseur stable pour: {self.file_path}")
        self._show_controls()
        plt.show()


def main():
    """Fonction principale"""
    parser = argparse.ArgumentParser(description="Visualiseur stable pour fichiers .nde")
    parser.add_argument("file_path", nargs='?', default="nde/test.nde",
                       help="Chemin vers le fichier .nde")
    
    args = parser.parse_args()
    
    try:
        viewer = NDEViewerStable(args.file_path)
        viewer.show()
    except Exception as e:
        print(f"Erreur: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
