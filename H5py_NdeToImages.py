import os
import pandas as pd
import numpy as np
import pickle
import ast
from PIL import ImageColor
import h5py
import json
import argparse

from utils.utils_linear_scan_alongv import gen_endview, safe_division

# Usage:
# python h5toImages_universal.py --files "chemin/vers/votre_fichier.nde" --output "dossier_de_sortie"

def get_file_structure(nde_file):
    """Determine the structure of the NDE file and return appropriate paths and data"""
    with h5py.File(nde_file, 'r') as f:
        root_keys = list(f.keys())
        
        # Structure type 1: HydroFORM_Gen2_Demo_2.nde
        if 'Domain' in root_keys:
            setup_path = 'Domain/Setup'
            data_structure = 'domain'
            
        # Structure type 2: Cor_Tube_5mm_Hydro_rast_all_0001.nde
        elif 'Public' in root_keys:
            setup_path = 'Public/Setup'
            data_structure = 'public'
        else:
            raise ValueError(f"Unknown file structure in {nde_file}")
            
        # Get setup data
        json_str = f[setup_path][()]
        json_decoded = json.loads(json_str)
        
        return data_structure, json_decoded

def data_from_nde_universal(nde_files, verbose=False):
    """Extract data from NDE files with different structures"""
    files_dict = {}
    
    for nde_file in nde_files:
        print(f"Processing nde file: {nde_file}. (key: {nde_file[-11:-4]})")
        runkey = nde_file[-11:-4]
        files_dict[runkey] = {}
        
        try:
            data_structure, json_decoded = get_file_structure(nde_file)
            NGroups = len(json_decoded["groups"])
            
            with h5py.File(nde_file, 'r') as f:
                # Debug: Print all keys in the file
                if verbose:
                    print("File structure:")
                    def print_keys(name, obj):
                        print(f"  {name}: {type(obj).__name__}")
                        if isinstance(obj, h5py.Group):
                            print(f"    Keys: {list(obj.keys())}")
                    f.visititems(print_keys)
                
                for gr in range(1, NGroups+1):
                    files_dict[runkey][gr] = {}
                    group_info = json_decoded["groups"][gr-1]
                    
                    # Debug: Print group info
                    if verbose:
                        print(f"Group {gr} info:")
                        print(f"  Keys: {list(group_info.keys())}")
                    
                    # Try to find data using different approaches
                    found_data = False
                    
                    # Approach 1: Use the path from JSON
                    if not found_data:
                        try:
                            if data_structure == 'domain':
                                if 'data' in group_info and 'ascan' in group_info['data']:
                                    path = group_info['data']['ascan']['dataset']['amplitude']['path']
                                    min_value = group_info['data']['ascan']['dataset']['amplitude']['dataSampling']['min']
                                    max_value = group_info['data']['ascan']['dataset']['amplitude']['dataSampling']['max']
                                    files_dict[runkey][gr]['data_array'] = f[path][:]
                                    found_data = True
                                    if verbose:
                                        print(f"Found data using domain/data path: {path}")
                                elif 'dataset' in group_info and 'ascan' in group_info['dataset']:
                                    path = group_info['dataset']['ascan']['amplitude']['path']
                                    min_value = group_info['dataset']['ascan']['amplitude']['dataSampling']['min']
                                    max_value = group_info['dataset']['ascan']['amplitude']['dataSampling']['max']
                                    files_dict[runkey][gr]['data_array'] = f[path][:]
                                    found_data = True
                                    if verbose:
                                        print(f"Found data using domain/dataset path: {path}")
                            elif data_structure == 'public':
                                if 'datasets' in group_info:
                                    for dataset in group_info['datasets']:
                                        if 'ascan' in dataset and 'amplitude' in dataset['ascan']:
                                            dataset_name = dataset['ascan']['amplitude']['path'].split('/')[-1]
                                            path = f"Public/Groups/{gr-1}/Datasets/{dataset_name}"
                                            min_value = dataset['ascan']['amplitude']['dataSampling']['min']
                                            max_value = dataset['ascan']['amplitude']['dataSampling']['max']
                                            files_dict[runkey][gr]['data_array'] = f[path][:]
                                            found_data = True
                                            if verbose:
                                                print(f"Found data using public/datasets path: {path}")
                                            break
                                elif 'dataset' in group_info and 'ascan' in group_info['dataset']:
                                    dataset_name = group_info['dataset']['ascan']['amplitude']['path'].split('/')[-1]
                                    path = f"Public/Groups/{gr-1}/Datasets/{dataset_name}"
                                    min_value = group_info['dataset']['ascan']['amplitude']['dataSampling']['min']
                                    max_value = group_info['dataset']['ascan']['amplitude']['dataSampling']['max']
                                    files_dict[runkey][gr]['data_array'] = f[path][:]
                                    found_data = True
                                    if verbose:
                                        print(f"Found data using public/dataset path: {path}")
                        except (KeyError, ValueError) as e:
                            if verbose:
                                print(f"Error using JSON paths: {e}")
                    
                    # Approach 2: Direct exploration of the file structure
                    if not found_data:
                        try:
                            if data_structure == 'domain':
                                # Try to find data in Domain/DataGroups
                                data_path = f"Domain/DataGroups/{gr-1}/Datasets/0/Amplitude"
                                if data_path in f:
                                    files_dict[runkey][gr]['data_array'] = f[data_path][:]
                                    min_value = 0
                                    max_value = np.max(files_dict[runkey][gr]['data_array'])
                                    found_data = True
                                    if verbose:
                                        print(f"Found data by direct exploration: {data_path}")
                            elif data_structure == 'public':
                                # Try to find data in Public/Groups
                                datasets_path = f"Public/Groups/{gr-1}/Datasets"
                                if datasets_path in f:
                                    for key in f[datasets_path].keys():
                                        try:
                                            if 'AScanAmplitude' in key:
                                                path = f"{datasets_path}/{key}"
                                                files_dict[runkey][gr]['data_array'] = f[path][:]
                                                min_value = 0
                                                max_value = np.max(files_dict[runkey][gr]['data_array'])
                                                found_data = True
                                                if verbose:
                                                    print(f"Found data by direct exploration: {path}")
                                                break
                                        except Exception as e:
                                            if verbose:
                                                print(f"Error accessing {key}: {e}")
                                    
                                    # If still not found, try any 3D dataset
                                    if not found_data:
                                        for key in f[datasets_path].keys():
                                            try:
                                                path = f"{datasets_path}/{key}"
                                                data = f[path][:]
                                                if len(data.shape) == 3:
                                                    files_dict[runkey][gr]['data_array'] = data
                                                    min_value = 0
                                                    max_value = np.max(data)
                                                    found_data = True
                                                    if verbose:
                                                        print(f"Found 3D dataset: {path}")
                                                    break
                                            except Exception as e:
                                                if verbose:
                                                    print(f"Error accessing {key}: {e}")
                        except Exception as e:
                            if verbose:
                                print(f"Error in direct exploration: {e}")
                    
                    # If we still haven't found data, skip this group
                    if not found_data:
                        print(f"Could not find data for group {gr} in {nde_file}")
                        continue
                    
                    # Set up metadata
                    files_dict[runkey][gr]['status_info'] = {}
                    files_dict[runkey][gr]['status_info']['min_value'] = min_value
                    files_dict[runkey][gr]['status_info']['max_value'] = max_value
                    files_dict[runkey][gr]['status_info']['group_name'] = group_info['name']
                    
                    # Get dimensions from the data array
                    shape = files_dict[runkey][gr]['data_array'].shape
                    files_dict[runkey][gr]['status_info']['number_files_input'] = shape[0]
                    files_dict[runkey][gr]['status_info']['img_width_px'] = shape[2]
                    files_dict[runkey][gr]['status_info']['img_height_px'] = shape[1]
                    
                    # Try to get dimension info from JSON
                    try:
                        if data_structure == 'domain':
                            if 'data' in group_info and 'ascan' in group_info['data']:
                                files_dict[runkey][gr]['status_info']['lengthwise'] = group_info['data']['ascan']['dataset']['amplitude']['dimensions'][0]
                                files_dict[runkey][gr]['status_info']['crosswise'] = group_info['data']['ascan']['dataset']['amplitude']['dimensions'][1]
                                files_dict[runkey][gr]['status_info']['ultrasound'] = group_info['data']['ascan']['dataset']['amplitude']['dimensions'][2]
                            elif 'dataset' in group_info and 'ascan' in group_info['dataset']:
                                files_dict[runkey][gr]['status_info']['lengthwise'] = group_info['dataset']['ascan']['amplitude']['dimensions'][0]
                                files_dict[runkey][gr]['status_info']['crosswise'] = group_info['dataset']['ascan']['amplitude']['dimensions'][1]
                                files_dict[runkey][gr]['status_info']['ultrasound'] = group_info['dataset']['ascan']['amplitude']['dimensions'][2]
                        elif data_structure == 'public':
                            if 'datasets' in group_info:
                                for dataset in group_info['datasets']:
                                    if 'ascan' in dataset and 'amplitude' in dataset['ascan']:
                                        files_dict[runkey][gr]['status_info']['lengthwise'] = dataset['ascan']['amplitude']['dimensions'][0]
                                        files_dict[runkey][gr]['status_info']['crosswise'] = dataset['ascan']['amplitude']['dimensions'][1]
                                        files_dict[runkey][gr]['status_info']['ultrasound'] = dataset['ascan']['amplitude']['dimensions'][2]
                                        break
                            elif 'dataset' in group_info and 'ascan' in group_info['dataset']:
                                files_dict[runkey][gr]['status_info']['lengthwise'] = group_info['dataset']['ascan']['amplitude']['dimensions'][0]
                                files_dict[runkey][gr]['status_info']['crosswise'] = group_info['dataset']['ascan']['amplitude']['dimensions'][1]
                                files_dict[runkey][gr]['status_info']['ultrasound'] = group_info['dataset']['ascan']['amplitude']['dimensions'][2]
                    except (KeyError, IndexError) as e:
                        if verbose:
                            print(f"Error getting dimensions from JSON: {e}")
                        
                        # Create default dimension info based on data shape
                        for axis, idx in zip(['lengthwise', 'crosswise', 'ultrasound'], range(3)):
                            files_dict[runkey][gr]['status_info'][axis] = {
                                'quantity': shape[idx],
                                'resolution': 0.001,  # Default resolution
                                'offset': 0.0         # Default offset
                            }
                    
                    # Make sure all required dimension info is present
                    for axis, idx in zip(['lengthwise', 'crosswise', 'ultrasound'], range(3)):
                        if axis not in files_dict[runkey][gr]['status_info']:
                            files_dict[runkey][gr]['status_info'][axis] = {
                                'quantity': shape[idx],
                                'resolution': 0.001,  # Default resolution
                                'offset': 0.0         # Default offset
                            }
                    
                    # Create position arrays
                    for axis in ['lengthwise', 'crosswise', 'ultrasound']:
                        positions_m = []
                        for idx in range(files_dict[runkey][gr]['status_info'][axis]['quantity']):
                            positions_m.append(files_dict[runkey][gr]['status_info'][axis]['offset'] + 
                                              idx * files_dict[runkey][gr]['status_info'][axis]['resolution'])
                        files_dict[runkey][gr]['status_info'][axis]['positions_m'] = np.array(positions_m)
                    
                    if verbose:
                        print(f"Gr {gr}")
                        print(f"data_array: {files_dict[runkey][gr]['data_array'].shape}")
                        print(f"Min: {np.min(files_dict[runkey][gr]['data_array'])}, Max: {np.max(files_dict[runkey][gr]['data_array'])}")
                        for axis in ['lengthwise', 'crosswise', 'ultrasound']:
                            print(f"{axis}: quantity={files_dict[runkey][gr]['status_info'][axis]['quantity']}, resolution={files_dict[runkey][gr]['status_info'][axis]['resolution']}")
        
        except Exception as e:
            print(f"Error processing file {nde_file}: {e}")
            import traceback
            traceback.print_exc()
    
    return files_dict

def add_valid_color(current_list_of_color):
    if "red" not in current_list_of_color:
        current_list_of_color.append("red")
    elif "blue" not in current_list_of_color:
        current_list_of_color.append("blue")
    else:
        for name, code in ImageColor.colormap.items():
            if name not in current_list_of_color:
                current_list_of_color.append(name)
                break
    return current_list_of_color

def genDataset(nde_files=None, output_folder='images'):
    path_omniscancolormap = './OmniScanColorMap.npy'
    images_folder_dest = output_folder      ### IMAGES FOLDER PATH
    
    if nde_files is None:
        nde_files = [
            # "ndes/HydroFORM_Gen2_Demo_2.nde",
            "ndes/Pipe_8mm_Hydroform_Rast_TGY_all_2025_05_23 05h10m27s.nde"
        ]

    trfm = 'base'
    annotation_version = 'no_label'
    groups_to_gen = [1]
    do_image_transpose = True
    anomaly_flaws = ["void_flaw", "scti_flaw", "bw_loss", "fw_flaw"]

    # What we need to generate
    txt_info = True
    complete = True
    flaw_no_flaw = False
    flaw_bb = False
    grayscale = True
    colorized = True

    files_dict = data_from_nde_universal(nde_files, verbose=True)

    base_Nbits = 8
    format_grey =  "uint" + str(base_Nbits)
    format_color = "rgb" + str(int(3*base_Nbits))

    for nde_file in nde_files:
        # Utiliser uniquement le nom du fichier sans le chemin
        nde_name = os.path.basename(nde_file).split('/')[-1][:-4]
        runkey = os.path.basename(nde_file)[-11:-4]
        print("\n")
        print(f"Runkey: {runkey}")
        print("-----------------------------------------------------")
        
        for gr in groups_to_gen:
            if gr not in files_dict[runkey]:
                print(f"Group {gr} not found in {nde_file}")
                continue
                
            # Skip if status_info is not available
            if 'status_info' not in files_dict[runkey][gr]:
                print(f"Status info not available for group {gr} in {nde_file}")
                continue
                
            print(f"Gr0{str(gr)}")
            print('------')

            rungr_output_path = os.path.join(images_folder_dest, nde_name, 'Gr0' + str(gr), trfm, annotation_version)

            n_images = files_dict[runkey][gr]['status_info']['lengthwise']['quantity']
            os.makedirs(rungr_output_path, exist_ok=True)
            flawlimits_csv = rungr_output_path+'/flaws_limits_mm.csv'
            boundingboxes_csv = rungr_output_path+'/endview_flaw_bb.csv'
            mask_path = rungr_output_path+'/segmentation_mask.npz'

            if flaw_no_flaw:
                from gen_bb_lmwp import gen_bb
                
                if os.path.isfile(boundingboxes_csv):
                    # Si endview_flaw_bb est dispo, on le load
                    df_defaut = pd.read_csv(boundingboxes_csv)

                elif os.path.isfile(flawlimits_csv):
                    # Si endview_flaw_bb n'est pas dispo, mais qu'on a flaws_limits_mm, on genere le endview_flaw_bb
                    df_defaut = gen_bb(pd.read_csv(flawlimits_csv), files_dict[runkey][gr])
                    # Extraction sous forme de csv
                    df_defaut.to_csv(rungr_output_path+'/endview_flaw_bb.csv', header=True, index=False)

                else:
                    flaw_no_flaw = False
                    print(f"There is no annotation file available in {rungr_output_path}.")

                if os.path.isfile(mask_path):
                    labels = np.load(mask_path)
                    
                    if "ANOMALY" in labels.keys():
                        anomaly_lbls = labels["ANOMALY"]
                    else:
                        anomaly_lbls = np.zeros((n_images,))

            if flaw_no_flaw:
                # On prend en note ce qui est un defaut dans une liste
                is_flaw = []
                for i in range(len(df_defaut)):
                    is_flaw.append(0 if ast.literal_eval(df_defaut.loc[i]['label'])[0]['label'] == 'no_flaw' else 1)
                
                # On prend en note ce qui est une anomalie dans un array
                is_flaw_anomaly = np.zeros((n_images,))
                for i in range(len(df_defaut)):
                    is_flaw_anomaly[i] = 1 if ast.literal_eval(df_defaut.loc[i]['label'])[0]['label'] in anomaly_flaws else 0
                is_anomaly = np.logical_or(is_flaw_anomaly, anomaly_lbls)

                # Instanciation liste de labels et de couleurs associées (pour les flaw_bb)
                encountered_labels = []
                labels_colors = []
                
            # Impression des images
            for idx in range(n_images):
                pos = files_dict[runkey][gr]['status_info']['lengthwise']['positions_m'][idx]
                
                if complete:
                    if grayscale:
                        image_dir = os.path.join(rungr_output_path, 'endviews_'+format_grey,'complete')
                        os.makedirs(image_dir, exist_ok=True)
                        gen_endview(runkey=runkey, 
                        gr=gr, 
                        idx=idx,
                        files_dict=files_dict,
                        img_bits=8,
                        colorize=False,
                        show=False,
                        printImg=True,
                        output_path=image_dir,
                        printbb=False,
                        path_omniscancolormap=path_omniscancolormap,
                        transpose=do_image_transpose)
                        
                    if colorized:
                        image_dir = os.path.join(rungr_output_path, 'endviews_'+format_color,'complete')
                        os.makedirs(image_dir, exist_ok=True)
                        gen_endview(runkey=runkey, 
                        gr=gr, 
                        idx=idx,
                        files_dict=files_dict,
                        img_bits=8,
                        colorize=True,
                        show=False,
                        printImg=True,
                        output_path=image_dir,
                        printbb=False,
                        path_omniscancolormap=path_omniscancolormap,
                        transpose=do_image_transpose)
                    
                if flaw_no_flaw:
                    if is_anomaly[idx]:
                        if grayscale:
                            image_dir = os.path.join(rungr_output_path, 'endviews_'+format_grey,'flaw')
                            os.makedirs(image_dir, exist_ok=True)
                            gen_endview(runkey=runkey, 
                                        gr=gr, 
                                        idx=idx,
                                        files_dict=files_dict,
                                        img_bits=8,
                                        colorize=False,
                                        show=False,
                                        printImg=True,
                                        output_path=image_dir,
                                        printbb=False,
                                        path_omniscancolormap=path_omniscancolormap,
                                        transpose=do_image_transpose)
                        if colorized:
                            image_dir = os.path.join(rungr_output_path, 'endviews_'+format_color,'flaw')
                            os.makedirs(image_dir, exist_ok=True)
                            gen_endview(runkey=runkey, 
                                        gr=gr, 
                                        idx=idx,
                                        files_dict=files_dict,
                                        img_bits=8,
                                        colorize=True,
                                        show=False,
                                        printImg=True,
                                        output_path=image_dir,
                                        printbb=False,
                                        path_omniscancolormap=path_omniscancolormap,
                                        transpose=do_image_transpose)
                    else:
                        if grayscale:
                            image_dir = os.path.join(rungr_output_path, 'endviews_'+format_grey,'noflaw')
                            os.makedirs(image_dir, exist_ok=True)
                            gen_endview(runkey=runkey, 
                                        gr=gr, 
                                        idx=idx,
                                        files_dict=files_dict,
                                        img_bits=8,
                                        colorize=False,
                                        show=False,
                                        printImg=True,
                                        output_path=image_dir,
                                        printbb=False,
                                        path_omniscancolormap=path_omniscancolormap,
                                        transpose=do_image_transpose)
                        if colorized:
                            image_dir = os.path.join(rungr_output_path, 'endviews_'+format_color,'noflaw')
                            os.makedirs(image_dir, exist_ok=True)
                            gen_endview(runkey=runkey, 
                                        gr=gr, 
                                        idx=idx,
                                        files_dict=files_dict,
                                        img_bits=8,
                                        colorize=True,
                                        show=False,
                                        printImg=True,
                                        output_path=image_dir,
                                        printbb=False,
                                        path_omniscancolormap=path_omniscancolormap,
                                        transpose=do_image_transpose)

                    if is_flaw[idx] and flaw_bb:
                        bbs = []
                        list_of_dict_bbs = ast.literal_eval(df_defaut['label'].iloc[idx])
                        tmp_df = pd.DataFrame(list_of_dict_bbs)
                        for label in tmp_df["label"].unique():
                            
                            if label not in encountered_labels:
                                encountered_labels.append(label)
                                labels_colors = add_valid_color(labels_colors)

                            current_label_info = {}
                            current_label_info["label"] = label
                            current_label_info["color"] = labels_colors[encountered_labels.index(label)]
                            
                            current_label_bbs = []
                            for _, line in tmp_df[tmp_df["label"]==label].iterrows():
                                current_label_bbs.append([line["topX"]*files_dict[runkey][gr]['status_info']['ultrasound']['quantity'],
                                                            line['topY']*files_dict[runkey][gr]['status_info']['crosswise']['quantity'],
                                                            line['bottomX']*files_dict[runkey][gr]['status_info']['ultrasound']['quantity'],
                                                            line['bottomY']*files_dict[runkey][gr]['status_info']['crosswise']['quantity']])
                            current_label_info["bb"] = current_label_bbs
                            bbs.append(current_label_info)
                        
                        if colorized:
                            image_dir = os.path.join(rungr_output_path, 'endviews_'+format_color,'flawbb')
                            os.makedirs(image_dir, exist_ok=True)
                            gen_endview(runkey=runkey, 
                                        gr=gr, 
                                        idx=idx,
                                        files_dict=files_dict,
                                        img_bits=8,
                                        colorize=True,
                                        show=False,
                                        printImg=True,
                                        output_path=image_dir,
                                        printbb=True,
                                        bbs=bbs,
                                        path_omniscancolormap=path_omniscancolormap,
                                        transpose=do_image_transpose)
                        if grayscale:
                            image_dir = os.path.join(rungr_output_path, 'endviews_'+format_grey,'flawbb')
                            os.makedirs(image_dir, exist_ok=True)
                            gen_endview(runkey=runkey, 
                                        gr=gr, 
                                        idx=idx,
                                        files_dict=files_dict,
                                        img_bits=8,
                                        colorize=False,
                                        show=False,
                                        printImg=True,
                                        output_path=image_dir,
                                        printbb=True,
                                        bbs=bbs,
                                        path_omniscancolormap=path_omniscancolormap,
                                        transpose=do_image_transpose)
                        
                    
                if idx%1000 == 0:
                    print(f"Endview {idx} on {n_images} total...")
                    print('\n')

            # Enregistre dictionnaire en pickle object si non disponible
            if 'files_dict.pkl' not in os.listdir(rungr_output_path):
                with open(os.path.join(rungr_output_path,'files_dict.pkl'), 'wb') as handle:
                    pickle.dump(files_dict[runkey][gr], handle, protocol=pickle.HIGHEST_PROTOCOL)
        print("====================================================")

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='Extract images from NDE files')
    parser.add_argument('--files', nargs='+', help='List of NDE files to process')
    parser.add_argument('--output', default='images', help='Output folder for images')
    
    args = parser.parse_args()
    
    if args.files:
        genDataset(nde_files=args.files, output_folder=args.output)
    else:
        genDataset()




