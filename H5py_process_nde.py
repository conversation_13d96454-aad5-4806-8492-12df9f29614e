import os
import re
import unicodedata
import argparse
from H5py_NdeToImages import genDataset

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Traite automatiquement tous les fichiers NDE d\'un dossier')
    parser.add_argument('--source', type=str, default="./ndes",
                       help='Dossier source contenant les fichiers NDE')
    parser.add_argument('--destination', type=str, default="./images",
                       help='Dossier destination pour les images générées')
    parser.add_argument('--auto-confirm', action='store_true',
                       help='Confirme automatiquement le traitement (pour interface graphique)')
    parser.add_argument('--preserve-parent-folders', action='store_true',
                       help='Préserve la structure des dossiers parents (ex: "From ...")')
    return parser.parse_args()

# Parse arguments
args = parse_arguments()
SOURCE_FOLDER = args.source
OUTPUT_BASE_FOLDER = args.destination
AUTO_CONFIRM = args.auto_confirm
PRESERVE_PARENT_FOLDERS = args.preserve_parent_folders

print(f"[INFO] Dossier source: {SOURCE_FOLDER}")
print(f"[INFO] Dossier destination: {OUTPUT_BASE_FOLDER}")
print(f"[INFO] Préservation des dossiers parents: {'Oui' if PRESERVE_PARENT_FOLDERS else 'Non'}")

# Crée le dossier de base s'il n'existe pas
os.makedirs(OUTPUT_BASE_FOLDER, exist_ok=True)

# Normalisation de noms
def normalize_name(name):
    name = os.path.splitext(name)[0]
    name = unicodedata.normalize('NFD', name).encode('ascii', 'ignore').decode('utf-8')
    name = re.sub(r'\W+', '', name)
    return name.lower().strip()

# Fonction pour trouver récursivement tous les fichiers .nde
def find_nde_files(directory):
    nde_files = []
    for root, _, files in os.walk(directory):
        for file in files:
            if file.lower().endswith('.nde'):
                full_path = os.path.join(root, file)
                relative_path = os.path.relpath(full_path, SOURCE_FOLDER)
                nde_files.append((full_path, relative_path))
    return nde_files

# Liste des fichiers .nde (maintenant avec leurs chemins complets)
nde_files = find_nde_files(SOURCE_FOLDER)

# Fonction pour trouver récursivement tous les dossiers de destination existants
def find_existing_output_dirs(base_folder):
    """Trouve récursivement tous les dossiers dans le dossier de destination"""
    existing_dirs_normalized = {}
    if not os.path.exists(base_folder):
        return existing_dirs_normalized

    for root, dirs, files in os.walk(base_folder):
        for dir_name in dirs:
            full_path = os.path.join(root, dir_name)
            normalized = normalize_name(dir_name)
            # Stocker le chemin complet pour chaque nom normalisé
            if normalized not in existing_dirs_normalized:
                existing_dirs_normalized[normalized] = []
            existing_dirs_normalized[normalized].append(full_path)

    return existing_dirs_normalized

# Liste des dossiers déjà présents dans OUTPUT_BASE_FOLDER (récursivement)
existing_dirs_normalized = find_existing_output_dirs(OUTPUT_BASE_FOLDER)

# Vérification
files_to_process = []
files_skipped = []

print("\n[INFO] Verification des correspondances entre fichiers .nde et dossiers de sortie...\n")

for nde_path, relative_path in nde_files:
    original_name = os.path.splitext(os.path.basename(relative_path))[0]
    normalized = normalize_name(original_name)

    # Construire le chemin de sortie en préservant ou non la structure des dossiers
    if PRESERVE_PARENT_FOLDERS:
        # Préserver la structure des dossiers parents
        relative_dir = os.path.dirname(relative_path)
        if relative_dir:
            output_subfolder = os.path.join(OUTPUT_BASE_FOLDER, relative_dir, original_name)
        else:
            output_subfolder = os.path.join(OUTPUT_BASE_FOLDER, original_name)
    else:
        # Structure plate (comportement original)
        output_subfolder = os.path.join(OUTPUT_BASE_FOLDER, original_name)

    print(f"[TEST] {relative_path} (-> {output_subfolder})")

    # Vérifier si un dossier avec ce nom normalisé existe déjà (récursivement)
    if normalized in existing_dirs_normalized:
        # Trouver le dossier existant le plus pertinent
        existing_paths = existing_dirs_normalized[normalized]
        found_existing = None

        # Chercher d'abord un dossier qui correspond exactement au chemin attendu
        for existing_path in existing_paths:
            if os.path.samefile(os.path.dirname(existing_path), os.path.dirname(output_subfolder)) if os.path.exists(os.path.dirname(output_subfolder)) else False:
                found_existing = existing_path
                break

        # Si pas de correspondance exacte, prendre le premier trouvé
        if not found_existing:
            found_existing = existing_paths[0]

        print(f"[OK] Dossier deja existant trouve : {found_existing}")
        files_skipped.append(relative_path)
    else:
        print(f"[NOUVEAU] Aucune correspondance -> a traiter")
        files_to_process.append((nde_path, output_subfolder))

# Résumé
print("\n==== RÉSUMÉ ====")
print(f"Total fichiers .nde : {len(nde_files)}")
print(f"Déjà traités        : {len(files_skipped)}")
print(f"A traiter maintenant : {len(files_to_process)}")

if not files_to_process:
    print("\n[OK] Aucun nouveau fichier a traiter.")
    exit()

# Confirmation
if AUTO_CONFIRM:
    print("\n[AUTO] Traitement automatique lance depuis l'interface graphique")
    proceed = 'o'
else:
    proceed = input("\n[ATTENTION] Voulez-vous lancer le traitement ? (o/n) : ").strip().lower()

if proceed != 'o':
    print("\n[ANNULE] Traitement annule.")
    exit()

# Traitement
for nde_path, output_subfolder in files_to_process:
    print(f"\n[TRAITEMENT] Traitement : {os.path.basename(nde_path)}")
    print(f"[DOSSIER] Dossier de sortie dedie : {output_subfolder}")
    
    # Créer le dossier de sortie
    os.makedirs(output_subfolder, exist_ok=True)

    try:
        # Copier le fichier NDE dans le dossier de sortie
        output_nde_path = os.path.join(output_subfolder, os.path.basename(nde_path))
        if not os.path.exists(output_nde_path):
            import shutil
            shutil.copy2(nde_path, output_nde_path)
        
        # Traiter le fichier dans le dossier de sortie
        genDataset(nde_files=[output_nde_path], output_folder=output_subfolder)
        
        # Supprimer la copie du fichier NDE après traitement
        os.remove(output_nde_path)
        
        print(f"[OK] Termine : {os.path.basename(nde_path)}")
    except Exception as e:
        print(f"[ERREUR] Erreur avec {os.path.basename(nde_path)} : {e}")
        # En cas d'erreur, nettoyer le fichier temporaire s'il existe
        if os.path.exists(output_nde_path):
            os.remove(output_nde_path)

print("\n[OK] Tous les fichiers selectionnes ont ete traites.")
