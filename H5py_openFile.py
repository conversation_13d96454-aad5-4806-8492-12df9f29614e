import h5py
import json
import argparse

def print_attrs(name, obj):
    print(f"Objet: {name}")
    for key, val in obj.attrs.items():
        print(f"  {key}: {val}")
    if isinstance(obj, h5py.Group):
        print(f"  Type: Groupe, Clés: {list(obj.keys())}")
    elif isinstance(obj, h5py.Dataset):
        print(f"  Type: Dataset, Shape: {obj.shape}, Dtype: {obj.dtype}")

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Ouvre et explore la structure d\'un fichier NDE')
    parser.add_argument('--file', type=str,
                       default="ndes/nde/From Benoit Cabirol/Pipe_10mm_Flexoform_Rast_All.nde",
                       help='Chemin vers le fichier NDE à explorer')
    return parser.parse_args()

def explore_nde_file(file_path):
    """Explore la structure d'un fichier NDE"""
    try:
        f = h5py.File(file_path, 'r')
        print(f"Fichier ouvert avec succès: {file_path}")

        # Afficher les clés de premier niveau
        print("Clés de premier niveau:", list(f.keys()))

        # Explorer la structure du fichier
        f.visititems(print_attrs)

        # Vérifier si Domain/Setup existe
        if 'Domain/Setup' in f:
            json_str = f['Domain/Setup'][()]
            json_decoded = json.loads(json_str)
            print("Nombre de groupes:", len(json_decoded["groups"]))
        else:
            print("Domain/Setup n'existe pas")

        # Vérifier si Public/Setup existe
        if 'Public/Setup' in f:
            print("Public/Setup trouvé")
        else:
            print("Public/Setup n'existe pas")

        f.close()
        print("Exploration terminée avec succès")
    except Exception as e:
        print(f"Erreur: {e}")

if __name__ == "__main__":
    args = parse_arguments()
    explore_nde_file(args.file)
