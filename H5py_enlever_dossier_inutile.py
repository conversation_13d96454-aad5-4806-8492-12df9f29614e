import shutil
from pathlib import Path
import argparse

def déplacer_contenu_no_label(chemin_no_label, destination):
    for nom_sous_dossier in ["endviews_rgb24", "endviews_uint8"]:
        source = chemin_no_label / nom_sous_dossier
        dest = destination / nom_sous_dossier
        if source.exists():
            if dest.exists():
                print(f"[WARNING] {dest} existe déjà, ignoré.")
            else:
                shutil.move(str(source), str(dest))
                print(f"[OK] Déplacé: {source} -> {dest}")
        else:
            print(f"[INFO] {source} n'existe pas, ignoré.")

def supprimer_dossiers_complete(racine):
    dossiers_complete = [d for d in Path(racine).rglob("complete") if d.is_dir()]
    total = len(dossiers_complete)
    if total == 0:
        print("[OK] Aucun dossier 'complete' trouve.")
        return

    print(f"[INFO] {total} dossier(s) 'complete' à traiter.")

    for i, dossier in enumerate(dossiers_complete, 1):
        dossier_parent = dossier.parent
        print(f"\n[{i}/{total}] Traitement de : {dossier}")

        for fichier in dossier.iterdir():
            dest = dossier_parent / fichier.name
            if dest.exists():
                print(f"[WARNING] Fichier déjà présent : {dest}, ignoré.")
                continue
            shutil.move(str(fichier), str(dest))
            print(f"[OK] Déplacé : {fichier.name}")

        shutil.rmtree(dossier)
        print(f"[OK] Supprime : {dossier}")

def nettoyer_structure_images(racine):
    racine = Path(racine)

    for dossier in racine.iterdir():
        if not dossier.is_dir():
            continue

        # === Cas 1 : Dossier miroir ===
        miroir = dossier / dossier.name / "Gr01" / "base" / "no_label"
        if miroir.exists():
            print(f"[MIROIR] Traitement de: {miroir}")
            déplacer_contenu_no_label(miroir, dossier)
            shutil.rmtree(dossier / dossier.name)
            print(f"[OK] Supprimé: {dossier / dossier.name}")
            continue

        # === Cas 2 : Structure directe ===
        direct = dossier / "Gr01" / "base" / "no_label"
        if direct.exists():
            print(f"[DIRECT] Traitement de: {direct}")
            déplacer_contenu_no_label(direct, dossier)
            shutil.rmtree(dossier / "Gr01")
            print(f"[OK] Supprimé: {dossier / 'Gr01'}")

    # === Suppression des dossiers 'complete' ===
    supprimer_dossiers_complete(racine)

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Nettoie la structure des dossiers d\'images générées')
    parser.add_argument('--root_dir', type=str,
                       default=r"C:\Users\<USER>\OneDrive - EvidentScientific\Documents\4Corrosion\NDEtoImagesUniversel\images",
                       help='Dossier racine des images à nettoyer')
    return parser.parse_args()

if __name__ == "__main__":
    args = parse_arguments()
    dossier_images = Path(args.root_dir)
    print(f"[INFO] Nettoyage du dossier: {dossier_images}")
    nettoyer_structure_images(dossier_images)
    print("[OK] Nettoyage termine.")
