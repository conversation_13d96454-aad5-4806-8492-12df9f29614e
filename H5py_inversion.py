import shutil
from pathlib import Path
import cv2
import argparse

def inverser_images_dossier(dossier):
    # Chercher récursivement tous les fichiers PNG
    fichiers_png = list(dossier.rglob("*.png"))
    total_fichiers = len(fichiers_png)

    if total_fichiers == 0:
        print(f"[WARNING] Aucun fichier PNG trouvé dans {dossier}")
        return

    print(f"[INFO] {total_fichiers} fichiers PNG trouvés dans {dossier}")

    images_inversees = 0
    for fichier in fichiers_png:
        img = cv2.imread(str(fichier), cv2.IMREAD_GRAYSCALE)
        if img is None:
            print(f"[WARNING] Image illisible : {fichier}")
            continue
        img_inversee = 255 - img
        cv2.imwrite(str(fichier), img_inversee)
        images_inversees += 1
        if images_inversees <= 5 or images_inversees % 50 == 0:  # Afficher les 5 premiers puis tous les 50
            print(f"[OK] Inversée : {fichier.name}")

    print(f"[OK] {images_inversees}/{total_fichiers} images inversées avec succès")

def traitement_automatique(racine, auto_confirm=False):
    racine = Path(racine)
    dossiers_uint8 = [d for d in racine.rglob("endviews_uint8") if d.is_dir()]
    total_trouves = len(dossiers_uint8)

    # Filtrer ceux qui n'ont pas encore de backup
    dossiers_a_traiter = []
    for d in dossiers_uint8:
        backup_dir = d.parent / f"{d.name}_backup"
        if not backup_dir.exists():
            dossiers_a_traiter.append((d, backup_dir))

    total_a_traiter = len(dossiers_a_traiter)

    # Affichage du statut
    print(f"[INFO] Dossiers 'endviews_uint8' trouvés      : {total_trouves}")
    print(f"[INFO] Dossiers sans backup à traiter         : {total_a_traiter}")

    if total_a_traiter == 0:
        print("[OK] Tous les dossiers ont deja ete traites.")
        return

    # Confirmation unique
    if auto_confirm:
        print("[AUTO] Traitement automatique lance depuis l'interface graphique")
        confirmation = "o"
    else:
        confirmation = input("Voulez-vous lancer le traitement ? (o/n) : ").strip().lower()

    if confirmation != "o":
        print("[ANNULE] Traitement annule.")
        return

    # Traitement
    for i, (uint8_dir, backup_dir) in enumerate(dossiers_a_traiter, 1):
        print(f"\n[{i}/{total_a_traiter}] Traitement de : {uint8_dir}")

        # Backup
        shutil.copytree(uint8_dir, backup_dir)
        print(f"[OK] Backup créé : {backup_dir}")

        # Inversion
        inverser_images_dossier(uint8_dir)

    print("\n[OK] Traitement termine.")

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Inverse les couleurs des images uint8 (255 - valeur_pixel)')
    parser.add_argument('--root_dir', type=str,
                       default=r"C:\Users\<USER>\Documents\4Corrosion\visualize_tool\images",
                       help='Dossier racine des images à inverser')
    parser.add_argument('--auto-confirm', action='store_true',
                       help='Confirme automatiquement le traitement (pour interface graphique)')
    return parser.parse_args()

if __name__ == "__main__":
    args = parse_arguments()
    dossier_images = Path(args.root_dir)
    print(f"[INFO] Inversion des images dans: {dossier_images}")
    traitement_automatique(dossier_images, auto_confirm=args.auto_confirm)
